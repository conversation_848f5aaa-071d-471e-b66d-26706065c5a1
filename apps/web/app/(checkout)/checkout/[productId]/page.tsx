// app/[locale]/(checkout)/checkout/[productId]/page.tsx
import "server-only";
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutHeader } from '../components/checkout-header';
import { CheckoutBanner } from '../components/CheckoutBanner';
import { CheckoutConversionElements } from '../components/checkout-conversion-elements';
import { UrgencyTimer } from "../../../../modules/ui/components/urgency-timer";
import { db } from '@repo/database';

interface CheckoutPageProps {
	params: Promise<{
		productId: string;
	}>;
	searchParams: Promise<{
		offer?: string;
		org?: string;
		quick?: string;
	}>;
}

export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
	const { productId } = await params;
	const { offer: offerId, org: organizationSlug, quick } = await searchParams;

	if (!productId) {
		redirect('/');
	}

	try {
		const product = await db.product.findFirst({
			where: {
				id: productId,
				status: {
					in: ['PUBLISHED', 'DRAFT'] // Aceitar tanto PUBLISHED quanto DRAFT
				},
			},
		});

		if (!product) {
			redirect('/');
		}

		// Buscar creator e offers separadamente
		const [creator, offers] = await Promise.all([
			db.user.findUnique({
				where: { id: product.creatorId },
				select: { id: true, name: true },
			}),
			db.offer.findMany({
				where: {
					productId: product.id,
					isActive: true,
					type: 'ORDER_BUMP',
				},
				select: {
					id: true,
					name: true,
					valueCents: true,
					type: true,
				},
			}),
		]);

		console.log('product', {
			id: product.id,
			settings: product.settings,
		});

		// Handle external checkout redirection
		if (product.checkoutType === 'EXTERNAL' && product.settings) {
			const settings = product.settings as Record<string, unknown>;
			if (typeof settings.customCheckoutUrl === 'string') {
				redirect(settings.customCheckoutUrl);
			}
		}

		// Parse checkout settings
		const checkoutSettings = product.settings as Record<string, unknown> || {};

		// Get conversion elements settings
		const urgencySettings = checkoutSettings.urgency as Record<string, unknown> || {};
		const trustBadgesSettings = checkoutSettings.trustBadges as Record<string, unknown> || {};
		const scarcitySettings = checkoutSettings.scarcity as Record<string, unknown> || {};
		const testimonialsSettings = checkoutSettings.testimonials as Record<string, unknown> || {};
		const headerSettings = checkoutSettings.header as Record<string, unknown> || {};
		const sidebarSettings = checkoutSettings.sidebar as Record<string, unknown> || {};

		return (
			<div className='min-h-screen'>
				<CheckoutHeader
					showLogo={headerSettings.showLogo as boolean || false}
					logoUrl={headerSettings.logoUrl as string || null}
					companyName={headerSettings.companyName as string || "SupGateway"}
				/>

				{/* Urgency Bar - Only urgency bar above banner */}
				<CheckoutConversionElements
					// Only Urgency Bar
					urgencyEnabled={urgencySettings.enabled as boolean || false}
					urgencyMessage={urgencySettings.message as string || "Esta oferta se encerra em:"}
					urgencyEndTime={urgencySettings.endTime ? new Date(urgencySettings.endTime as string) : undefined}
					urgencyBackgroundColor={urgencySettings.backgroundColor as string || "bg-red-50"}
					urgencyTextColor={urgencySettings.textColor as string || "text-white"}
					urgencyAccentColor={urgencySettings.accentColor as string || "bg-red-600"}

					// Disable other elements here - they'll be in the summary
					trustBadgesEnabled={false}
					scarcityEnabled={false}
					testimonialsEnabled={false}
				/>

				<div className='mx-3 md:mx-auto md:container py-5'>
					{/* Display a banner if available from settings */}
					<CheckoutBanner
						bannerUrl={
							checkoutSettings.banner as string | null || null
						}
						enabled={checkoutSettings.bannerEnabled as boolean || true}
						maxHeight={checkoutSettings.bannerMaxHeight as string || '300px'}
						borderRadius={checkoutSettings.bannerBorderRadius as string || 'rounded-lg'}
						shadow={checkoutSettings.bannerShadow as boolean || true}
					/>

					<CheckoutForm
						product={{
							id: product.id,
							title: product.name,
							description: product.description,
							type: product.type === 'MENTORSHIP' ? 'MENTORING' : product.type as 'COURSE' | 'EBOOK' | 'MENTORING',
							price: Number(product.priceCents) / 100,
							installmentsLimit: 12, // Default value
							enableInstallments: true, // Default value
							thumbnail: product.thumbnail,
							checkoutType: product.checkoutType,
							acceptedPayments: ['CREDIT_CARD', 'PIX'], // Default values
							checkoutSettings: product.settings,
							customCheckoutUrl: null,
							successUrl: null,
							cancelUrl: null,
							termsUrl: null,
							offers: offers?.map((o) => ({
								id: o.id,
								title: o.name,
								description: null,
								price: Number(o.valueCents) / 100,
								type: o.type,
							})),
						}}
						// Pass conversion settings to CheckoutForm
						trustBadgesSettings={trustBadgesSettings}
						testimonialsSettings={testimonialsSettings}
						sidebarSettings={sidebarSettings}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading product for checkout:', error);
		redirect('/');
	}
}

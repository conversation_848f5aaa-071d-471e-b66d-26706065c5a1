// apps/web/(checkout)/checkout/components/checkout-form.tsx

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { CheckoutFormData, checkoutFormSchema } from './types';
import { CustomerForm } from './customer-form';
import { PaymentForm } from './payment-form';
import { CombinedForm } from './combined-form';
import { SidebarBanner } from './sidebar-banner';

import { useToast } from '@ui/hooks/use-toast';
import { useState, useEffect } from 'react';
import { ProductSummaryCard } from './checkout-summary-card';
import { EnhancedCheckoutSummary } from './enhanced-checkout-summary';
import { useAnalytics } from '@analytics';

interface CheckoutFormProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		type: 'COURSE' | 'MENTORING' | 'EBOOK';
		price: number;
		originalPrice?: number;
		regularPrice?: number | null;
		installmentsLimit: number;
		enableInstallments?: boolean;
		thumbnail?: string | null;
		checkoutBanner?: string | null;
		checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
		acceptedPayments?: string[];
		checkoutSettings?: any;
		customCheckoutUrl?: string | null;
		successUrl?: string | null;
		cancelUrl?: string | null;
		termsUrl?: string | null;
		selectedOffer?: {
			id: string;
			title: string;
			price: number;
			type: string;
		} | null;
		offers?: {
			id: string;
			title: string;
			description?: string | null;
			price: number;
			type: string;
		}[];
	};
	offerId?: string;
	// Conversion settings
	trustBadgesSettings?: any;
	testimonialsSettings?: any;
	sidebarSettings?: any;
}

export function CheckoutForm({ product, offerId, trustBadgesSettings, testimonialsSettings, sidebarSettings }: CheckoutFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { toast } = useToast();
	const { trackEvent } = useAnalytics();

	// Estados para controle do checkout
	const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
	const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
	const [formStartTime] = useState(Date.now());

	// Payment processing state
	const [isProcessingPayment, setIsProcessingPayment] = useState(false);

	// Form setup com valores padrão
	const getDefaultValues = (): Partial<CheckoutFormData> => {
		// Valores dos query params (ex: email pré-preenchido)
		const email = searchParams.get('email');
		return {
			customerData: {
				email: email || '',
				name: '',
				cpf: '',
				phone: '',
			},
			paymentMethod: 'CREDIT_CARD',
			productId: product.id,
			creditCard: {
				cardNumber: '',
				cardHolder: '',
				cardExpiry: '',
				cardCvv: '',
				installments: 1, // Valor padrão sempre 1
			},
		};
	};

	const methods = useForm<CheckoutFormData>({
		resolver: zodResolver(checkoutFormSchema),
		defaultValues: getDefaultValues(),
		mode: 'onBlur',
	});

	// Tracking de início do checkout
	useEffect(() => {
		trackEvent('checkout_started', {
			productId: product.id,
			productTitle: product.title,
			productPrice: product.price,
			timestamp: Date.now(),
		});
	}, []);

	// Função para calcular total
	const calculateTotal = (basePrice: number, bumps: string[], offers?: any[]) => {
		if (!offers || bumps.length === 0) return basePrice;

		const selectedOffers = offers.filter(offer => bumps.includes(offer.id));
		const offersTotal = selectedOffers.reduce((acc, offer) => acc + offer.price, 0);

		return basePrice + offersTotal;
	};

	// Handler para cupom aplicado
	const handleCouponApplied = (coupon: any) => {
		setAppliedCoupon(coupon);
		trackEvent('coupon_applied', {
			productId: product.id,
			couponCode: coupon.code,
			discountAmount: coupon.value,
		});
	};



		// Handler principal de submit
	const onSubmit = async (data: CheckoutFormData) => {
		console.log('=== SUBMIT FUNCTION CALLED ===');
		console.log('Form data submitted:', data);

		setIsProcessingPayment(true);

		try {
			const totalAmount = appliedCoupon?.finalPrice || calculateTotal(product.price, selectedBumps, product.offers);

			const paymentData = {
				...data,
				offerId: offerId,
				orderBumpIds: selectedBumps,
				couponCode: appliedCoupon?.code,
				...(data.paymentMethod === 'CREDIT_CARD' && data.creditCard
					? {
							creditCard: {
								...data.creditCard,
								cardNumber: data.creditCard.cardNumber.replace(/\D/g, ''),
								cardExpiry: data.creditCard.cardExpiry,
							},
						}
					: {}),
			};

			console.log('Calling processPayment API...');
			const response = await fetch('/api/checkout/process-payment', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(paymentData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Erro ao processar pagamento');
			}

			const result = await response.json();

			trackEvent('payment_success', {
				productId: product.id,
				orderId: result.orderId,
				paymentMethod: data.paymentMethod,
				totalAmount,
				timeToComplete: Date.now() - formStartTime,
			});

			// Redirecionar para o checkout do Stripe
			if (result.checkoutUrl) {
				window.location.href = result.checkoutUrl;
			} else {
				// Fallback para página de sucesso
				router.push(`/checkout/success?orderId=${result.orderId}`);
			}
		} catch (error: any) {
			console.error('Payment failed:', error);

			trackEvent('payment_failed', {
				productId: product.id,
				paymentMethod: data.paymentMethod,
				error: error.message,
				timeSpent: Date.now() - formStartTime,
			});

			// Redirecionamento personalizado em caso de erro
			if (product.cancelUrl) {
				router.push(product.cancelUrl);
				return;
			}

			toast.error(`Erro ao processar pagamento: ${getErrorMessage(error)}`);
		} finally {
			setIsProcessingPayment(false);
		}
	};

	// Função para obter mensagem de erro amigável
	const getErrorMessage = (error: any): string => {
		const message = error?.message || '';

		if (message.includes('network') || message.includes('connection')) {
			return 'Problema de conexão. Verifique sua internet e tente novamente.';
		}

		if (message.includes('card') || message.includes('cartão')) {
			return 'Problema com o cartão. Verifique os dados e tente novamente.';
		}

		if (message.includes('insufficient')) {
			return 'Saldo insuficiente. Tente outro cartão ou método de pagamento.';
		}

		return 'Erro inesperado. Tente novamente ou entre em contato com o suporte.';
	};

	// Apply custom checkout styles from product settings if available
	const checkoutStyles = product.checkoutSettings?.styles || {};

	return (
		<FormProvider {...methods}>
			<form
				onSubmit={methods.handleSubmit(onSubmit)}
				className='container max-w-6xl mx-auto px-0 lg:px-4'
				// style={checkoutStyles.formContainer || {}}
			>
				{/* Mobile Summary - Always visible at top on mobile */}
				<div className='lg:hidden mb-6'>
					<EnhancedCheckoutSummary
						product={product}
						selectedBumps={selectedBumps}
						onBumpChange={setSelectedBumps}
						isMobile={true}
						appliedCoupon={appliedCoupon}
						onCouponApplied={handleCouponApplied}
						showConversionElements={true}
					/>
				</div>

				<div className='grid gap-8 lg:grid-cols-12'>
					{/* Main Form Content */}
					<div className='lg:col-span-8'>
						<CombinedForm
							totalAmount={
								appliedCoupon
									? appliedCoupon.finalPrice
									: calculateTotal(product.price, selectedBumps, product.offers)
							}
							installmentsLimit={product.installmentsLimit}
							enableInstallments={product.enableInstallments}
							acceptedPayments={product.acceptedPayments}
							loading={isProcessingPayment}
							offers={product.offers?.map((offer) => ({
								...offer,
								description: offer.description || null,
								type: offer.type as 'ORDER_BUMP' | 'UPSELL' | 'DOWNSELL',
							}))}
							selectedBumps={selectedBumps}
							onBumpChange={setSelectedBumps}
						/>
					</div>

					{/* Desktop Summary */}
					<div className='hidden lg:block lg:col-span-4'>
						<div className='lg:sticky lg:top-24 space-y-4'>
							{/* Sidebar Banner */}
							{sidebarSettings && (
								<SidebarBanner
									enabled={sidebarSettings.enabled as boolean || false}
									bannerUrl={sidebarSettings.bannerUrl as string || null}
									title={sidebarSettings.title as string || "Informações Importantes"}
									content={sidebarSettings.content as string || "Adicione informações úteis para seus clientes aqui."}
									backgroundColor={sidebarSettings.backgroundColor as string || 'bg-blue-50'}
									textColor={sidebarSettings.textColor as string || 'text-blue-800'}
									borderColor={sidebarSettings.borderColor as string || 'border-blue-200'}
									borderRadius={sidebarSettings.borderRadius as string || 'rounded-lg'}
									shadow={sidebarSettings.shadow as boolean || true}
								/>
							)}

							<EnhancedCheckoutSummary
								product={product}
								selectedBumps={selectedBumps}
								onBumpChange={setSelectedBumps}
								isMobile={false}
								appliedCoupon={appliedCoupon}
								onCouponApplied={handleCouponApplied}
								showConversionElements={true}
								// Trust Badges
								trustBadgesEnabled={trustBadgesSettings?.enabled as boolean || true}
								trustBadges={trustBadgesSettings?.badges as any[] || undefined}
								trustBadgesLayout={trustBadgesSettings?.layout as 'horizontal' | 'vertical' | 'grid' || 'vertical'}
								trustBadgesShowDescriptions={trustBadgesSettings?.showDescriptions as boolean || true}
								trustBadgesBackgroundColor={trustBadgesSettings?.backgroundColor as string || 'bg-blue-50'}
								trustBadgesTextColor={trustBadgesSettings?.textColor as string || 'text-blue-800'}
								trustBadgesBorderColor={trustBadgesSettings?.borderColor as string || 'border-blue-200'}
								// Testimonials
								testimonialsEnabled={testimonialsSettings?.enabled as boolean || true}
								testimonials={testimonialsSettings?.testimonials as any[] || undefined}
								testimonialsMaxTestimonials={testimonialsSettings?.maxTestimonials as number || 3}
								testimonialsShowStars={testimonialsSettings?.showStars as boolean || true}
								testimonialsShowAvatars={testimonialsSettings?.showAvatars as boolean || true}
								testimonialsBackgroundColor={testimonialsSettings?.backgroundColor as string || 'bg-gray-50'}
								testimonialsTextColor={testimonialsSettings?.textColor as string || 'text-gray-800'}
								testimonialsBorderColor={testimonialsSettings?.borderColor as string || 'border-gray-200'}
							/>
						</div>
					</div>
				</div>


			</form>
		</FormProvider>
	);
}

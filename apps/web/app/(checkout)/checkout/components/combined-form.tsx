// components/combined-form.tsx
import { User, CreditCard } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';
import { Button } from '@ui/components/button';
import { UrgentPurchaseButton, PixButton, BoletoButton } from '@ui/components/conversion';
import { RadioGroup } from '@ui/components/radio-group';

import { CPFInput } from './cpf-input';
import { FormField } from './form-field';
import { PhoneInputWithFlag } from './phone-input-with-flag';
import { PaymentOption } from './payment-option';
import { CreditCardForm } from './checkout-credit-card-form';
import { CheckoutFooter } from './checkout-footer';
import { OrderBumpList } from './order-bump-list';
import { PaymentFormPixInfo } from './payment-form-pix-info';
import { PaymentFormBoletoInfo } from './payment-form-boleto-info';
import { CheckoutFormData } from './types';
import type { Offer } from './types';

interface CombinedFormProps {
	loading: boolean;
	totalAmount: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
	acceptedPayments?: string[];
	offers?: Offer[];
	selectedBumps: string[];
	onBumpChange: (ids: string[]) => void;
}

// Definição padrão de métodos de pagamento para referência
const PAYMENT_METHODS = {
	CREDIT_CARD: {
		value: 'CREDIT_CARD',
		title: 'Cartão',
		description: '',
		icon: 'credit-card',
	},
	PIX: {
		value: 'PIX',
		title: 'Pix',
		description: 'Pagamento instantâneo',
		icon: 'pix',
	},
	BOLETO: {
		value: 'BOLETO',
		title: 'Boleto',
		description: 'Confirmação em até 24 horas',
		icon: 'boleto',
	},
};

const defaultAcceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'];
const brands = ['visa', 'mastercard', 'amex', 'elo'];

export function CombinedForm({
	loading,
	totalAmount,
	installmentsLimit,
	enableInstallments = false,
	acceptedPayments = defaultAcceptedPayments,
	offers = [],
	selectedBumps,
	onBumpChange,
}: CombinedFormProps) {
	const form = useFormContext<CheckoutFormData>();
	const paymentMethod = form.watch('paymentMethod');

	const {
		control,
		formState: { errors },
		setError,
		clearErrors,
	} = form;

	// Função para validar email
	const validateEmail = (email: string) => {
		if (!email.trim()) {
			clearErrors('customerData.email');
			return;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			setError('customerData.email', { message: 'Email inválido' });
		} else {
			clearErrors('customerData.email');
		}
	};

	// Função para validar telefone
	const validatePhone = (phone: string) => {
		if (!phone.trim()) {
			clearErrors('customerData.phone');
			return;
		}

		const cleanPhone = phone.replace(/\D/g, '');
		if (cleanPhone.length < 10) {
			setError('customerData.phone', { message: 'Telefone deve ter pelo menos 10 dígitos' });
		} else {
			clearErrors('customerData.phone');
		}
	};

	// Função para validar nome
	const validateName = (name: string) => {
		if (!name.trim()) {
			clearErrors('customerData.name');
			return;
		}

		if (name.trim().length < 3) {
			setError('customerData.name', { message: 'Nome deve ter pelo menos 3 caracteres' });
		} else {
			clearErrors('customerData.name');
		}
	};

	// Função para validar CPF
	const validateCPF = (cpf: string) => {
		if (!cpf.trim()) {
			clearErrors('customerData.cpf');
			return;
		}

		const cleanCPF = cpf.replace(/\D/g, '');
		if (cleanCPF.length !== 11) {
			setError('customerData.cpf', { message: 'CPF deve ter 11 dígitos' });
		} else {
			clearErrors('customerData.cpf');
		}
	};

	// Filtrar apenas métodos de pagamento permitidos pelo produto
	const allowedPaymentMethods = Object.values(PAYMENT_METHODS).filter(
		(method) => acceptedPayments.includes(method.value)
	);

	const renderPaymentMethod = () => {
		switch (paymentMethod) {
			case 'CREDIT_CARD':
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
			case 'PIX':
				return <PaymentFormPixInfo />;
			case 'BOLETO':
				return <PaymentFormBoletoInfo />;
			default:
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
		}
	};

	return (
		<Card className='bg-white shadow-sm'>
			{/* Informações Section */}
			<CardHeader className='p-6 space-y-1 border-b'>
				<div className='flex items-center gap-2'>
					<User className='h-5 w-5 text-muted-foreground' />
					<CardTitle className='text-lg'>Informações</CardTitle>
				</div>
				<p className='text-sm text-muted-foreground'>
					Preencha seus dados para completar a compra
				</p>
			</CardHeader>

			<CardContent className='p-6 space-y-6'>
				{/* Customer Data */}
				<div className='grid gap-4 md:grid-cols-2'>
					<FormField
						name='customerData.name'
						label='Nome completo'
						error={errors.customerData?.name?.message}
					>
						<Controller
							name='customerData.name'
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									placeholder='Digite seu nome completo'
									onBlur={(e) => {
										field.onBlur();
										validateName(e.target.value);
									}}
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.email'
						label='E-mail'
						error={errors.customerData?.email?.message}
					>
						<Controller
							name='customerData.email'
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									type='email'
									placeholder='<EMAIL>'
									onChange={(e) => field.onChange(e.target.value)}
									onBlur={(e) => {
										field.onBlur();
										validateEmail(e.target.value);
									}}
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.cpf'
						label='CPF'
						error={errors.customerData?.cpf?.message}
					>
						<Controller
							name='customerData.cpf'
							control={control}
							render={({ field }) => (
								<CPFInput
									value={field.value}
									onChange={field.onChange}
									onBlur={(e) => {
										field.onBlur();
										validateCPF(field.value);
									}}
									placeholder='000.000.000-00'
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.phone'
						label='Celular'
						error={errors.customerData?.phone?.message}
					>
						<Controller
							name='customerData.phone'
							control={control}
							render={({ field }) => (
								<PhoneInputWithFlag
									value={field.value}
									onChange={field.onChange}
									onBlur={(e) => {
										field.onBlur();
										validatePhone(field.value);
									}}
									error={!!errors.customerData?.phone}
									errorMessage={errors.customerData?.phone?.message}
								/>
							)}
						/>
					</FormField>
				</div>

				{/* Payment Section */}
				<div className='space-y-6'>
					<div className='flex items-center justify-between'>
						<div>
							<div className='flex items-center gap-2'>
								<CreditCard className='h-5 w-5 text-muted-foreground' />
								<CardTitle className='text-lg'>Pagamento</CardTitle>
							</div>
						</div>

						<div className='brand-icons flex items-center gap-1 md:gap-3'>
							{brands.map((brand) => (
								<img
									key={brand}
									src={`/images/payments/card/${brand}.svg`}
									alt={brand}
									className='h-4 md:h-6 w-auto border rounded-[3px]'
								/>
							))}
						</div>
					</div>

					{allowedPaymentMethods.length > 0 && (
						<Controller
							name='paymentMethod'
							control={form.control}
							render={({ field }) => (
								<RadioGroup
									value={field.value}
									onValueChange={(value: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {
										field.onChange(value);
										if (value === 'PIX' || value === 'BOLETO') {
											form.setValue('creditCard', undefined);
										}
									}}
									className='grid grid-cols-3 gap-4'
								>
									{allowedPaymentMethods.map((method) => (
										<PaymentOption
											key={method.value}
											value={method.value}
											title={method.title}
											description={method.description}
											icon={method.icon as 'credit-card' | 'pix' | 'boleto'}
											selected={paymentMethod === method.value}
										/>
									))}
								</RadioGroup>
							)}
						/>
					)}

					{renderPaymentMethod()}

					{/* Order Bumps */}
					{offers && offers.length > 0 && (
						<div className='mt-6'>
							<h3 className='text-lg font-medium mb-4'>
								Aproveite e compre junto:
							</h3>
							<OrderBumpList
								offers={offers}
								selected={selectedBumps}
								onSelect={onBumpChange}
							/>
						</div>
					)}

					{/* Payment Action Button */}
					{paymentMethod === 'PIX' ? (
						<PixButton
							onClick={(e) => {
								console.log('=== PIX BUTTON CLICKED ===');
								console.log('Event target:', e.target);
								console.log('Event currentTarget:', e.currentTarget);
								console.log('Form values:', form.getValues());

								// Garantir que o evento seja propagado para o form
								if (e.target !== e.currentTarget) {
									console.log('Event target is not button, forcing form submit...');
									const form = e.currentTarget.closest('form');
									if (form) {
										console.log('Found form, triggering submit...');
										form.requestSubmit();
									}
								}
							}}
							disabled={loading}
							loading={loading}
						/>
					) : paymentMethod === 'BOLETO' ? (
						<BoletoButton
							onClick={(e) => {
								console.log('=== BOLETO BUTTON CLICKED ===');
								console.log('Event target:', e.target);
								console.log('Event currentTarget:', e.currentTarget);
								console.log('Form values:', form.getValues());

								// Garantir que o evento seja propagado para o form
								if (e.target !== e.currentTarget) {
									console.log('Event target is not button, forcing form submit...');
									const form = e.currentTarget.closest('form');
									if (form) {
										console.log('Found form, triggering submit...');
										form.requestSubmit();
									}
								}
							}}
							disabled={loading}
							loading={loading}
						/>
					) : (
						<UrgentPurchaseButton
							discount={33}
							onClick={(e) => {
								console.log('=== URGENT BUTTON CLICKED ===');
								console.log('Event target:', e.target);
								console.log('Event currentTarget:', e.currentTarget);
								console.log('Form values:', form.getValues());

								// Garantir que o evento seja propagado para o form
								if (e.target !== e.currentTarget) {
									console.log('Event target is not button, forcing form submit...');
									const form = e.currentTarget.closest('form');
									if (form) {
										console.log('Found form, triggering submit...');
										form.requestSubmit();
									}
								}
							}}
							disabled={loading}
							loading={loading}
						/>
					)}

					<CheckoutFooter />
				</div>
			</CardContent>
		</Card>
	);
}

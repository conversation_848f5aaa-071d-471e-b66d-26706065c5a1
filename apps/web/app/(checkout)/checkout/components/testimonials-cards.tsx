'use client';

import { Star, CheckCircle } from 'lucide-react';
import Image from 'next/image';

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  verified?: boolean;
}

interface TestimonialsCardsProps {
  enabled?: boolean;
  testimonials?: Testimonial[];
  maxTestimonials?: number;
  showStars?: boolean;
  showAvatars?: boolean;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  layout?: 'horizontal' | 'vertical' | 'grid';
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    rating: 5,
    comment: 'Produto excelente! Superou minhas expectativas. Recomendo para todos.',
    location: 'São Paulo, SP',
    verified: true
  },
  {
    id: '2',
    name: '<PERSON>',
    rating: 5,
    comment: 'Qualidade incrível e entrega rápida. Vale cada centavo investido.',
    location: 'Rio de Janeiro, RJ',
    verified: true
  },
  {
    id: '3',
    name: '<PERSON>',
    rating: 5,
    comment: 'Transformou minha vida! Não me arrependo da compra.',
    location: 'Belo Horizonte, MG',
    verified: true
  }
];

export function TestimonialsCards({
  enabled = true,
  testimonials = defaultTestimonials,
  maxTestimonials = 3,
  showStars = true,
  showAvatars = true,
  backgroundColor = 'bg-gray-50',
  textColor = 'text-gray-800',
  borderColor = 'border-gray-200',
  layout = 'vertical'
}: TestimonialsCardsProps) {
  const displayTestimonials = testimonials.slice(0, maxTestimonials);

  if (!enabled || displayTestimonials.length === 0) return null;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex gap-4 overflow-x-auto pb-2';
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
      case 'vertical':
      default:
        return 'space-y-4';
    }
  };

  const getCardClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex-shrink-0 w-80';
      case 'grid':
        return 'w-full';
      case 'vertical':
      default:
        return 'w-full';
    }
  };

  return (
    <div className={`w-full ${backgroundColor} ${borderColor} border rounded-lg p-6`}>
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <Star className="h-5 w-5 text-yellow-400 fill-current" />
        <h3 className={`text-lg font-semibold ${textColor}`}>
          O que nossos clientes dizem
        </h3>
      </div>

      {/* Testimonials Cards */}
      <div className={getLayoutClasses()}>
        {displayTestimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className={`${getCardClasses()} bg-white ${borderColor} border rounded-lg p-4 shadow-sm`}
          >
            {/* Avatar and Rating */}
            <div className="flex items-start gap-3 mb-3">
              {showAvatars && (
                <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                  {testimonial.avatar ? (
                    <Image
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white font-semibold text-sm">
                      {testimonial.name.charAt(0)}
                    </div>
                  )}
                </div>
              )}

              <div className="flex-1 min-w-0">
                {/* Rating */}
                {showStars && (
                  <div className="flex gap-1 mb-2">
                    {renderStars(testimonial.rating)}
                  </div>
                )}

                {/* Author Info */}
                <div className="flex items-center gap-2">
                  <div className={`font-semibold text-sm ${textColor} truncate`}>
                    {testimonial.name}
                  </div>
                  {testimonial.verified && (
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  )}
                </div>

                {testimonial.location && (
                  <div className="text-xs text-gray-600 truncate">
                    {testimonial.location}
                  </div>
                )}
              </div>
            </div>

            {/* Comment */}
            <blockquote className={`text-sm ${textColor} italic leading-relaxed`}>
              "{testimonial.comment}"
            </blockquote>
          </div>
        ))}
      </div>
    </div>
  );
}

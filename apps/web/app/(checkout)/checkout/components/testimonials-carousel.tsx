'use client';

import { useState, useEffect } from 'react';
import { Star, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import Image from 'next/image';

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  verified?: boolean;
}

interface TestimonialsCarouselProps {
  enabled?: boolean;
  testimonials?: Testimonial[];
  maxTestimonials?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  showStars?: boolean;
  showAvatars?: boolean;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    rating: 5,
    comment: 'Produto excelente! Superou minhas expectativas. Recomendo para todos.',
    location: 'São Paulo, SP',
    verified: true
  },
  {
    id: '2',
    name: '<PERSON>',
    rating: 5,
    comment: 'Qualidade incrível e entrega rápida. Vale cada centavo investido.',
    location: 'Rio de Janeiro, RJ',
    verified: true
  },
  {
    id: '3',
    name: 'Ana Costa',
    rating: 5,
    comment: 'Transformou minha vida! Não me arrependo da compra.',
    location: 'Belo Horizonte, MG',
    verified: true
  }
];

export function TestimonialsCarousel({
  enabled = true,
  testimonials = defaultTestimonials,
  maxTestimonials = 3,
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = true,
  showStars = true,
  showAvatars = true,
  backgroundColor = 'bg-gray-50',
  textColor = 'text-gray-800',
  borderColor = 'border-gray-200'
}: TestimonialsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  const displayTestimonials = testimonials.slice(0, maxTestimonials);

  useEffect(() => {
    if (!isPlaying || displayTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, displayTestimonials.length, autoPlayInterval]);

  if (!enabled || displayTestimonials.length === 0) return null;

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayTestimonials.length) % displayTestimonials.length);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className={`w-full ${backgroundColor} ${borderColor} border rounded-lg p-6`}>
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-400 fill-current" />
            <h3 className={`text-lg font-semibold ${textColor}`}>
              O que nossos clientes dizem
            </h3>
          </div>

          {showControls && displayTestimonials.length > 1 && (
            <div className="flex items-center gap-2">
              <button
                onClick={togglePlayPause}
                className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                aria-label={isPlaying ? 'Pausar' : 'Reproduzir'}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </button>
            </div>
          )}
        </div>

        {/* Testimonial Content */}
        <div className="relative overflow-hidden">
          <div
            className="flex transition-transform duration-300 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {displayTestimonials.map((testimonial) => (
              <div key={testimonial.id} className="w-full flex-shrink-0">
                <div className="text-center">
                  {/* Avatar */}
                  {showAvatars && (
                    <div className="flex justify-center mb-4">
                      <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                        {testimonial.avatar ? (
                          <Image
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white font-semibold">
                            {testimonial.name.charAt(0)}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Rating */}
                  {showStars && (
                    <div className="flex justify-center gap-1 mb-3">
                      {renderStars(testimonial.rating)}
                    </div>
                  )}

                  {/* Comment */}
                  <blockquote className={`text-lg italic ${textColor} mb-4`}>
                    "{testimonial.comment}"
                  </blockquote>

                  {/* Author Info */}
                  <div className="flex flex-col items-center">
                    <div className={`font-semibold ${textColor}`}>
                      {testimonial.name}
                      {testimonial.verified && (
                        <span className="ml-2 text-green-500">✓</span>
                      )}
                    </div>
                    {testimonial.location && (
                      <div className="text-sm text-gray-600">
                        {testimonial.location}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Dots */}
        {displayTestimonials.length > 1 && (
          <div className="flex justify-center gap-2 mt-6">
            {displayTestimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-blue-500' : 'bg-gray-300'
                }`}
                aria-label={`Ir para depoimento ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Navigation Arrows */}
        {showControls && displayTestimonials.length > 1 && (
          <>
            <button
              onClick={prevTestimonial}
              className="absolute left-0 top-1/2 -translate-y-1/2 p-2 hover:bg-gray-200 rounded-full transition-colors"
              aria-label="Depoimento anterior"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 -translate-y-1/2 p-2 hover:bg-gray-200 rounded-full transition-colors"
              aria-label="Próximo depoimento"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </>
        )}
      </div>
    </div>
  );
}
